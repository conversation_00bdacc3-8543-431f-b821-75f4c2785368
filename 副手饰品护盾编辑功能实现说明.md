# 副手、饰品、护盾编辑功能实现说明

## 概述
为玉帝后台版游戏添加了副手、饰品、护盾的编辑功能，将原本的进阶/升级按钮改为编辑按钮，可以直接修改装备属性。

## 修改的文件

### 1. 饰品编辑功能
**文件**: `scripts玉帝后台版/UI/forging/jewelryUpgrade/JewelryUpgradeBoard.as`

**主要修改**:
- 添加了 `ExternalInterface` 导入
- 添加了外部接口回调: `ExternalInterface.addCallback("jewelry_EditCheating",Jewelry_EditCheating)`
- 将按钮名称从"进阶"改为"饰品编辑"
- 修改 `btnClick` 函数，直接调用编辑界面而不检查进阶条件
- 添加了 `afterEdit()` 和 `Jewelry_EditCheating()` 函数

**编辑选项**:
- 等级[00*数量]
- 强化[01*数量] 
- 名称[02*文本]
- 品质[03*英文] (white, green, blue, purple, orange, red)
- 技能等级[04*数量]

### 2. 护盾编辑功能
**文件**: `scripts玉帝后台版/UI/forging/shieldUpgrade/ShieldUpgradeBoard.as`

**主要修改**:
- 添加了 `ExternalInterface` 导入
- 添加了外部接口回调: `ExternalInterface.addCallback("shield_EditCheating",Shield_EditCheating)`
- 将按钮名称从"进阶"改为"护盾编辑"
- 修改 `btnClick` 函数，直接调用编辑界面而不检查进阶条件
- 添加了 `afterEdit()` 和 `Shield_EditCheating()` 函数

**编辑选项**:
- 等级[00*数量]
- 强化[01*数量]
- 名称[02*文本]
- 品质[03*英文] (white, green, blue, purple, orange, red)
- 技能等级[04*数量]

### 3. 副手编辑功能
**文件**: `scripts玉帝后台版/UI/forging/equipUpgrade/EquipUpgradeBoard.as`

**主要修改**:
- 添加了 `ExternalInterface` 导入
- 添加了外部接口回调: `ExternalInterface.addCallback("equip_EditCheating",Equip_EditCheating)`
- 将按钮名称从"升级"改为"副手编辑"
- 修改 `btnClick` 函数，直接调用编辑界面而不检查升级条件
- 添加了 `afterEdit()` 和 `Equip_EditCheating()` 函数

**编辑选项**:
- 等级[00*数量]
- 强化[01*数量]
- 名称[02*文本]
- 品质[03*英文] (white, green, blue, purple, orange, red)
- 耐久[04*数量]

## 使用方法

1. **进入对应界面**:
   - 饰品编辑: 进入锻造界面 → 饰品进阶
   - 护盾编辑: 进入锻造界面 → 护盾进阶  
   - 副手编辑: 进入锻造界面 → 装备升级

2. **选择要编辑的装备**:
   - 在右侧装备列表中选择要编辑的装备

3. **点击编辑按钮**:
   - 点击"饰品编辑"/"护盾编辑"/"副手编辑"按钮

4. **输入编辑指令**:
   - 在弹出的输入框中输入编辑指令
   - 格式: `属性名*值` 或 `编号*值`
   - 例如: `等级*50` 或 `00*50`

## 编辑指令格式

### 通用格式
- `等级*数值` 或 `00*数值` - 设置等级
- `强化*数值` 或 `01*数值` - 设置强化等级
- `名称*文本` 或 `02*文本` - 设置自定义名称
- `品质*颜色` 或 `03*颜色` - 设置品质颜色

### 品质颜色选项
- `white` - 白色品质
- `green` - 绿色品质
- `blue` - 蓝色品质
- `purple` - 紫色品质
- `orange` - 橙色品质
- `red` - 红色品质

### 特殊属性
- **饰品/护盾**: `技能等级*数值` 或 `04*数值`
- **副手**: `耐久*数值` 或 `04*数值`

## 示例

### 饰品编辑示例
```
等级*99        # 设置饰品等级为99
强化*20        # 设置强化等级为20
名称*神级饰品   # 设置名称为"神级饰品"
品质*red       # 设置为红色品质
技能等级*10    # 设置技能等级为10
```

### 护盾编辑示例
```
00*50          # 设置护盾等级为50
01*15          # 设置强化等级为15
02*无敌护盾    # 设置名称为"无敌护盾"
03*orange      # 设置为橙色品质
04*8           # 设置技能等级为8
```

### 副手编辑示例
```
等级*80        # 设置副手等级为80
强化*25        # 设置强化等级为25
名称*传说副手   # 设置名称为"传说副手"
品质*purple    # 设置为紫色品质
耐久*1000      # 设置耐久为1000
```

## 注意事项

1. **装备选择**: 必须先选择要编辑的装备，否则编辑功能无效
2. **数值范围**: 建议设置合理的数值，避免游戏异常
3. **品质设置**: 品质颜色必须使用英文，区分大小写
4. **即时生效**: 编辑后的属性会立即生效并显示成功消息

## 技术实现

所有编辑功能都通过以下方式实现:
1. 添加 `ExternalInterface` 外部接口支持
2. 修改按钮点击事件，跳过原有的条件检查
3. 直接调用编辑界面，显示输入框
4. 解析用户输入并修改对应的装备属性
5. 显示操作成功消息

这种实现方式保持了原有界面的完整性，只是改变了按钮的功能，使其更适合后台管理需求。
