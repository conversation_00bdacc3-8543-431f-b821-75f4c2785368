package UI.pet.evo
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.grid.NormalGridIcon;
   import UI.base.must.NormalMustBar;
   import UI.base.must.NormalMustBox;
   import UI.pet.PetUI;
   import com.adobe.serialization.json.JSON2;
   import com.greensock.TweenLite;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.creator.PetDataEvoCtrl;
   import dataAll.pet.gene.define.GeneDefine;
   import fl.transitions.easing.Back;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   
   public class PetEvoBoard extends NormalUI
   {
      
      private var beforeTag:Sprite;
      
      private var beforeIcon:NormalGridIcon = new NormalGridIcon();
      
      private var afterTag:Sprite;
      
      private var afterIcon:NormalGridIcon = new NormalGridIcon();
      
      private var noSp:MovieClip;
      
      private var dpsBarSp:Sprite;
      
      private var lifeBarSp:Sprite;
      
      private var growMustSp:Sprite;
      
      private var growMustBar:NormalMustBar = new NormalMustBar();
      
      private var mustSp:Sprite;
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      public var Aa:String;
      
      public var Bb:int;
      
      public function PetEvoBoard()
      {
         ExternalInterface.addCallback("pet_ZZCheating",Pet_ZZCheating);
         ExternalInterface.addCallback("pet_QHCheating",Pet_QHCheating);
         ExternalInterface.addCallback("pet_DingCheating",Pet_DingCheating);
         super();
      }
      
      private static function no_afterEvo(v0:* = null) : void
      {
         Gaming.uiGroup.petUI.hide();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["dpsBarSp","lifeBarSp","beforeTag","afterTag","mustSp","btnSp","noSp","growMustSp"];
         super.setImg(img0);
         this.noSp.stop();
         this.beforeTag.addChild(this.beforeIcon);
         this.afterTag.addChild(this.afterIcon);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("尸宠编辑");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.actived = true;
         addChild(this.growMustBar);
         this.growMustBar.setImg(this.growMustSp);
         this.growMustBar.setText("所需资质培养总百分比");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         if(Boolean(PetUI.nowGrip))
         {
            this.showData(PetUI.getNowData());
         }
      }
      
      private function clearAll() : void
      {
         this.mustBox.setShowState(false);
         this.growMustBar.clearAll();
         this.noSp.visible = true;
      }
      
      private function showData(da0:PetData) : void
      {
         var beforeDefine0:GeneDefine = null;
         var must_d0:MustDefine = null;
         var mustB0:Boolean = false;
         var nowGrowPer0:Number = Number(NaN);
         var mustGrowPer0:Number = Number(NaN);
         var growB0:Boolean = false;
         beforeDefine0 = da0.getGeneDefine();
         var afterDefine0:GeneDefine = beforeDefine0.getEvoGeneDefine();
         var isRedB0:Boolean = ["red","orange"].indexOf(da0.gene.getColor()) >= 0;
         this.beforeIcon.setIconName(beforeDefine0.getBodyImgUrl());
         this.showMul(beforeDefine0);
         if(Boolean(afterDefine0) && isRedB0)
         {
            this.noSp.visible = false;
            this.afterIcon.setIconName(afterDefine0.getBodyImgUrl());
            this.showMul(afterDefine0,"sec");
            this.afterTag.scaleX = 0.9;
            this.afterTag.scaleY = this.afterTag.scaleX;
            TweenLite.to(this.afterTag,0.5,{
               "scaleX":1,
               "scaleY":1,
               "ease":Back.easeOut
            });
            must_d0 = PetDataEvoCtrl.getMust(da0);
            mustB0 = this.mustBox.inData(must_d0,da0.base.save.level,"尸宠",false);
            nowGrowPer0 = da0.gene.save.getGrowAllPer();
            mustGrowPer0 = beforeDefine0.growMust;
            growB0 = this.growMustBar.inData(nowGrowPer0,mustGrowPer0);
            this.btn.actived = true;
         }
         else
         {
            this.clearAll();
            this.afterIcon.clear();
            this.noSp.gotoAndStop(isRedB0 ? "no" : "onlyRed");
         }
      }
      
      private function showMul(d0:GeneDefine, spName0:String = "bar") : void
      {
         var name0:String = null;
         var per0:Number = Number(NaN);
         var nameArr0:Array = GeneDefine.mulNameArr;
         for each(name0 in nameArr0)
         {
            per0 = d0.getMulUIPer(name0);
            this.setBarPer(name0,per0,spName0);
         }
      }
      
      private function setBarPer(name0:String, per0:Number, spName0:String = "bar") : void
      {
         if(per0 > 1)
         {
            per0 = 1;
         }
         if(per0 < 0)
         {
            per0 = 0;
         }
         var sp0:Sprite = this[name0 + "BarSp"];
         var bar0:Sprite = sp0[spName0 + "Sp"];
         if(spName0 == "bar")
         {
            bar0.scaleX = per0;
         }
         else
         {
            bar0.scaleX = sp0["barSp"].scaleX;
            TweenLite.to(bar0,0.5,{"scaleX":per0});
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var da0:PetData = PetUI.getNowData();
         if(Boolean(da0))
         {
            // 直接打开编辑界面，不需要检查进化条件
            this.afterEvo();
         }
      }
      
      private function afterEvo() : void
      {
         Gaming.uiGroup.alertBox.textInput.showTextInput("等级[00*数量] 经验[01*数量] 名称[02*文本]\n生命[03*数量] 伤害[04*数量] 头防[05*数量]\n颜色[06*英文] 出战[07*可空] 替补[08*可空]\n" + ComMethod.color("资质编辑[09]  ","#AE22CE") + ComMethod.color("资质强化[10]","#fd397b") + "\n","",this.Pet_DingCheating);
      }
      
      public function Pet_DingCheating(str0:String) : void
      {
         var da0:PetData = PetUI.getNowData();
         var TextArray:Array = new Array();
         TextArray = str0.split("*",str0.length);
         var name:String = String(TextArray[1]);
         var playerName:String = String(TextArray[1]);
         this.Aa = TextArray[0];
         this.Bb = TextArray[1];
         if(this.Aa == "00" || this.Aa == "等级")
         {
            if(da0 is PetData)
            {
               da0.base.save.level = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠等级" + TextArray[1]);
            }
         }
         if(this.Aa == "01" || this.Aa == "经验")
         {
            if(da0 is PetData)
            {
               da0.base.save.exp = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠经验" + TextArray[1]);
            }
         }
         if(this.Aa == "02" || this.Aa == "名称")
         {
            if(da0 is PetData)
            {
               da0.base.save.playerName = playerName;
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠名称" + TextArray[1]);
            }
         }
         if(this.Aa == "03" || this.Aa == "生命")
         {
            if(da0 is PetData)
            {
               da0.base.save.lifeAdd = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠生命加成" + TextArray[1]);
            }
         }
         if(this.Aa == "04" || this.Aa == "伤害")
         {
            if(da0 is PetData)
            {
               da0.base.save.dpsAdd = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠战斗力加成" + TextArray[1]);
            }
         }
         if(this.Aa == "05" || this.Aa == "头防")
         {
            if(da0 is PetData)
            {
               da0.base.save.headAdd = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠头部防御加成" + TextArray[1]);
            }
         }
         if(this.Aa == "06" || this.Aa == "颜色")
         {
            if(da0 is PetData)
            {
               da0.gene.save.color = name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠颜色:" + TextArray[1]);
            }
         }
         if(this.Aa == "07" || this.Aa == "出战")
         {
            if(da0 is PetData)
            {
               da0.save.fightB = "true";
               Gaming.uiGroup.alertBox.showSuccess(da0.base.save.playerName + "出战成功");
            }
         }
         if(this.Aa == "08" || this.Aa == "替补")
         {
            if(da0 is PetData)
            {
               da0.save.suppleB = "true";
               Gaming.uiGroup.alertBox.showSuccess(da0.base.save.playerName + "替补成功");
            }
         }
         if(this.Aa == "09")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("战斗力[00*数值] 伤害[01*数值]\n生命[02*数值] 头部防御[03*数值]\n生命回复[04*数值] 3倍暴击[05*数值]\n技能回速[06*数值] 闪避[07*数值]\n近战减免[08*数值] 移动速度[09*数值]\n\n","",this.Pet_ZZCheating);
         }
         if(this.Aa == "10")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("战斗力[00*数值] 伤害[01*数值]\n生命[02*数值] 头部防御[03*数值]\n生命回复[04*数值] 3倍暴击[05*数值]\n技能回速[06*数值] 闪避[07*数值]\n近战减免[08*数值] 移动速度[09*数值]\n\n","",this.Pet_QHCheating);
         }
      }
      
      public function Pet_QHCheating(str0:String) : void
      {
         var da0:PetData = PetUI.getNowData();
         var TextArray:Array = new Array();
         var obj:Object = {};
         TextArray = str0.split("*",str0.length);
         this.Aa = TextArray[0];
         this.Bb = TextArray[1];
         if(da0 is PetData)
         {
            obj = JSON2.decode(JSON2.encode(da0.gene.save.growObj));
            if(this.Aa == "00" || this.Aa == "战斗力")
            {
               obj.dpsMul = this.Bb;
            }
            if(this.Aa == "01" || this.Aa == "伤害")
            {
               obj.hurtMul = this.Bb;
            }
            if(this.Aa == "02" || this.Aa == "生命")
            {
               obj.lifeMul = this.Bb;
            }
            if(this.Aa == "03" || this.Aa == "头部防御")
            {
               obj.headMul = this.Bb;
            }
            if(this.Aa == "04" || this.Aa == "生命回复")
            {
               obj.lifeRate = this.Bb;
            }
            if(this.Aa == "05" || this.Aa == "3倍暴击")
            {
               obj.critPro3 = this.Bb;
            }
            if(this.Aa == "06" || this.Aa == "技能回速")
            {
               obj.cdMul = this.Bb;
            }
            if(this.Aa == "07" || this.Aa == "闪避")
            {
               obj.dodge = this.Bb;
            }
            if(this.Aa == "08" || this.Aa == "近战减免")
            {
               obj.fightDedut = this.Bb;
            }
            if(this.Aa == "09" || this.Aa == "移动速度")
            {
               obj.moveMul = this.Bb;
            }
            da0.gene.save.growObj = obj;
            Gaming.uiGroup.alertBox.showSuccess("当前属性编辑完毕");
         }
      }
      
      public function Pet_ZZCheating(str0:String) : void
      {
         var da0:PetData = PetUI.getNowData();
         var TextArray:Array = new Array();
         var obj:Object = {};
         TextArray = str0.split("*",str0.length);
         this.Aa = TextArray[0];
         this.Bb = TextArray[1];
         if(da0 is PetData)
         {
            obj = JSON2.decode(JSON2.encode(da0.gene.save.obj));
            if(this.Aa == "00" || this.Aa == "战斗力")
            {
               obj.dpsMul = this.Bb;
            }
            if(this.Aa == "01" || this.Aa == "伤害")
            {
               obj.hurtMul = this.Bb;
            }
            if(this.Aa == "02" || this.Aa == "生命")
            {
               obj.lifeMul = this.Bb;
            }
            if(this.Aa == "03" || this.Aa == "头部防御")
            {
               obj.headMul = this.Bb;
            }
            if(this.Aa == "04" || this.Aa == "生命回复")
            {
               obj.lifeRate = this.Bb;
            }
            if(this.Aa == "05" || this.Aa == "3倍暴击")
            {
               obj.critPro3 = this.Bb;
            }
            if(this.Aa == "06" || this.Aa == "技能回速")
            {
               obj.cdMul = this.Bb;
            }
            if(this.Aa == "07" || this.Aa == "闪避")
            {
               obj.dodge = this.Bb;
            }
            if(this.Aa == "08" || this.Aa == "近战减免")
            {
               obj.fightDedut = this.Bb;
            }
            if(this.Aa == "09" || this.Aa == "移动速度")
            {
               obj.moveMul = this.Bb;
            }
            da0.gene.save.obj = obj;
            Gaming.uiGroup.alertBox.showSuccess("当前属性编辑完毕");
         }
      }
      
      private function yes_afterEvo(v0:* = null) : void
      {
         PetUI.loadPetSwf(this.affter_loadPetSwf,this.fail_loadPetSwf);
      }
      
      private function affter_loadPetSwf() : void
      {
         Gaming.uiGroup.petUI.refleshBar();
         this.showData(PetUI.getNowData());
         Gaming.uiGroup.alertBox.showSuccess("进化成功！");
      }
      
      private function fail_loadPetSwf() : void
      {
         Gaming.uiGroup.petUI.hide;
         Gaming.uiGroup.alertBox.showError("文件读取失败！");
      }
   }
}

