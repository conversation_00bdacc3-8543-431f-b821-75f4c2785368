package UI.forging.equipUpgrade
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.base.tip.OneTextGather;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.EquipUpgradeCtrl;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   
   public class EquipUpgradeBoard extends NormalUI
   {
      
      private var beforeTxt:OneTextGather = new OneTextGather();
      
      private var afterTxt:OneTextGather = new OneTextGather();
      
      private var beforeTag:Sprite;
      
      private var afterTag:Sprite;
      
      private var mustSp:Sprite;
      
      private var itemsGripSp:MovieClip;
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var itemsGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      public var nowData:EquipData = null;

      public var Aa:String;

      public var Bb:int;

      public function EquipUpgradeBoard()
      {
         ExternalInterface.addCallback("equip_EditCheating",Equip_EditCheating);
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["beforeTag","afterTag","mustSp","btnSp","itemsGripSp"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("副手编辑");
         addChild(this.itemsGrip);
         this.itemsGrip.setImg(this.itemsGripSp);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.itemsGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.beforeTxt);
         NormalUICtrl.setTag(this.beforeTxt,this.beforeTag);
         this.beforeTxt.init();
         this.beforeTxt.LAST_ICON_GAP = 5;
         addChild(this.afterTxt);
         NormalUICtrl.setTag(this.afterTxt,this.afterTag);
         this.afterTxt.init();
         this.afterTxt.LAST_ICON_GAP = 5;
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"equip");
         this.showOneEquipDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:EquipData) : void
      {
         if(visible)
         {
            this.showOneEquipDataAndPan(da0);
         }
      }
      
      private function showOneEquipDataAndPan(da0:EquipData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要升级的装备。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneEquipData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneEquipData(da0:EquipData) : void
      {
         this.nowData = da0;
         var after_da0:EquipData = EquipUpgradeCtrl.getAfterData(da0);
         this.itemsGrip.inData_equip(da0);
         this.beforeTxt.setText(EquipUpgradeCtrl.getStateStr(da0,after_da0));
         this.afterTxt.setText(EquipUpgradeCtrl.getStateStr(after_da0,da0,false));
         var must_d0:MustDefine = EquipUpgradeCtrl.getMust(da0);
         var bodyLv0:int = 0;
         if(this.nowData.normalPlayerData is NormalPlayerData)
         {
            bodyLv0 = this.nowData.normalPlayerData.level;
         }
         var mustB0:Boolean = this.mustBox.inData(must_d0,bodyLv0,"",true);
         var canB0:Boolean = da0.canUpgradeB();
         // 编辑按钮始终可用，不受升级条件限制
         this.btn.actived = true;
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.itemsGrip.clearData();
         this.beforeTxt.setText("");
         this.afterTxt.setText("");
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowData))
         {
            // 直接打开编辑界面，不需要检查升级条件
            this.afterEdit();
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("装备数据不存在！");
         }
      }
      
      private function afterEdit() : void
      {
         Gaming.uiGroup.alertBox.textInput.showTextInput("等级[00*数量] 强化[01*数量] 名称[02*文本]\n品质[03*英文] 耐久[04*数量]\n\n品质选项: white, green, blue, purple, orange, red\n","",this.Equip_EditCheating);
      }

      public function Equip_EditCheating(str0:String) : void
      {
         var TextArray:Array = new Array();
         TextArray = str0.split("*",str0.length);
         var name:String = String(TextArray[1]);
         this.Aa = TextArray[0];
         this.Bb = TextArray[1];

         if(Boolean(this.nowData))
         {
            if(this.Aa == "00" || this.Aa == "等级")
            {
               this.nowData.save.level = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手等级" + TextArray[1]);
            }
            if(this.Aa == "01" || this.Aa == "强化")
            {
               this.nowData.save.strengthenLevel = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手强化等级" + TextArray[1]);
            }
            if(this.Aa == "02" || this.Aa == "名称")
            {
               this.nowData.save.playerName = name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手名称" + TextArray[1]);
            }
            if(this.Aa == "03" || this.Aa == "品质")
            {
               this.nowData.save.color = name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手品质:" + TextArray[1]);
            }
            if(this.Aa == "04" || this.Aa == "耐久")
            {
               this.nowData.save.durability = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手耐久" + TextArray[1]);
            }
         }
      }
   }
}

